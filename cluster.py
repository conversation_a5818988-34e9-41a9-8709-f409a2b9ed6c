import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler
from sklearn.cluster import KMeans
from sklearn.metrics import silhouette_score
import matplotlib.pyplot as plt
import seaborn as sns

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 读取数据
df = pd.read_csv("原文作者综合统计表.csv")

# 检查数据
print("数据形状:", df.shape)
print("列名:", df.columns.tolist())
print("前5行数据:")
print(df.head())
print("\n数据统计:")
print(df.describe())

# 使用实际的中文列名
features = [
    "原创总数", "转发总数", "被转发数总和",
    "被点赞数总和", "被评论数总和", "阅读数总和",
    "粉丝数总和", "总内容数"
]

print(f"\n使用的特征列: {features}")

# 检查是否有缺失值
print(f"\n缺失值检查:")
for feature in features:
    missing_count = df[feature].isnull().sum()
    print(f"{feature}: {missing_count} 个缺失值")

# 数据预处理和采样
print(f"\n原始数据量: {len(df)} 个作者")

# 为了提高计算效率，我们采样一部分数据进行聚类分析
# 优先选择活跃用户（总内容数较多的用户）
sample_size = min(10000, len(df))  # 最多采样10000个样本
df_sorted = df.sort_values('总内容数', ascending=False)
df_sample = df_sorted.head(sample_size).copy()

print(f"采样后数据量: {len(df_sample)} 个作者")
print(f"采样数据统计:")
print(df_sample[features].describe())

# 数据标准化
X_raw = df_sample[features].copy()
scaler = StandardScaler()
X_scaled = scaler.fit_transform(X_raw)

print(f"\n开始计算最优聚类数量...")
scores = []
K_range = range(2, 11)  # 扩展到11个聚类
for k in K_range:
    print(f"正在计算 K={k} 的轮廓系数...")
    kmeans = KMeans(n_clusters=k, random_state=42, n_init=10, max_iter=300)
    labels = kmeans.fit_predict(X_scaled)
    score = silhouette_score(X_scaled, labels)
    scores.append(score)
    print(f"K={k}, 轮廓系数={score:.3f}")

plt.figure(figsize=(10, 6))
plt.plot(K_range, scores, marker='o', linewidth=2, markersize=8)
plt.xlabel("聚类数量 (K)")
plt.ylabel("轮廓系数")
plt.title("轮廓系数 vs 聚类数量")
plt.grid(True, alpha=0.3)
plt.show()

optimal_k = K_range[np.argmax(scores)]
print(f"\n最优聚类数量: {optimal_k}")
print(f"对应的轮廓系数: {max(scores):.3f}")

# 使用最优K值进行聚类
kmeans = KMeans(n_clusters=optimal_k, random_state=42, n_init=10)
labels = kmeans.fit_predict(X_scaled)
df_sample['聚类标签'] = labels

# 将聚类结果应用到全部数据
# 对于未采样的数据，使用训练好的模型进行预测
X_all = scaler.transform(df[features])
df['聚类标签'] = kmeans.predict(X_all)

# 计算聚类中心
cluster_centers = pd.DataFrame(
    scaler.inverse_transform(kmeans.cluster_centers_),
    columns=features
)
cluster_centers['聚类'] = range(optimal_k)

print("\n各聚类中心特征均值：")
print(cluster_centers)

# 计算每个聚类的样本数量
cluster_counts = df['聚类标签'].value_counts().sort_index()
print(f"\n各聚类样本数量:")
for i in range(optimal_k):
    print(f"聚类 {i}: {cluster_counts[i]} 个作者")

# 绘制聚类中心热力图
plt.figure(figsize=(14, 8))
heatmap_data = cluster_centers.set_index('聚类')
sns.heatmap(heatmap_data, cmap="YlOrRd", annot=True, fmt=".0f",
            cbar_kws={'label': '特征值'})
plt.title("各聚类特征均值热力图")
plt.ylabel("聚类")
plt.xlabel("特征")
plt.xticks(rotation=45)
plt.tight_layout()
plt.show()

# 保存结果
output_filename = "用户行为聚类结果.csv"
df.to_csv(output_filename, index=False, encoding='utf-8-sig')
print(f"\n聚类结果已保存到: {output_filename}")

# 显示每个聚类的代表性作者
print(f"\n各聚类代表性作者:")
for i in range(optimal_k):
    cluster_data = df[df['聚类标签'] == i]
    print(f"\n聚类 {i} (共{len(cluster_data)}个作者):")
    # 显示该聚类中总内容数最多的前5个作者
    top_authors = cluster_data.nlargest(5, '总内容数')[['原文作者', '总内容数', '原创总数', '粉丝数总和']]
    print(top_authors.to_string(index=False))