import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler
from sklearn.cluster import KMeans
from sklearn.metrics import silhouette_score
import matplotlib.pyplot as plt
import seaborn as sns

df = pd.read_csv("userinfo5.csv") 
features = [
"post_count", "retweet_count", "retweeted_count",
"like_count", "comment_count", "view_count",
"follower_count", "hashtag_count", "mention_count"
]

X_raw = df[features].copy()
scaler = StandardScaler()
X_scaled = scaler.fit_transform(X_raw)
scores = []
K_range = range(2, 10)
for k in K_range:
    kmeans = KMeans(n_clusters=k, random_state=42)
    labels = kmeans.fit_predict(X_scaled)
    score = silhouette_score(X_scaled, labels)
    scores.append(score)
    
plt.plot(K_range, scores, marker='o')
plt.xlabel("Number of Clusters (K)")
plt.ylabel("Silhouette Score")
plt.title("Silhouette Score vs K")
plt.show()

optimal_k = K_range[np.argmax(scores)]
kmeans = KMeans(n_clusters=optimal_k, random_state=42)
labels = kmeans.fit_predict(X_scaled)
df['cluster'] = labels

cluster_centers = pd.DataFrame(
scaler.inverse_transform(kmeans.cluster_centers_),
columns=features
)
cluster_centers['cluster'] = range(optimal_k)
print("各聚类中心行为均值：")
print(cluster_centers)

plt.figure(figsize=(12, 6))
sns.heatmap(cluster_centers.set_index('cluster'), cmap="YlGnBu", annot=True, fmt=".1f")
plt.title("Cluster-wise Average Feature Values")
plt.ylabel("Cluster")
plt.xlabel("Feature")
plt.show()

df.to_csv("user_behavior_clusters.csv", index=False)